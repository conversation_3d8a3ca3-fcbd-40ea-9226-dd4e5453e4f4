# Spring Boot Gateway 透明代理模块

## 项目概述

这是一个基于Spring Boot 2.7.7的透明代理网关模块，用于在前端和后端之间进行完全透明的API请求转发。该网关作为纯代理层，不修改任何请求和响应内容，不添加任何额外功能。

## 技术栈

- **Spring Boot**: 2.7.7
- **Java**: 8
- **构建工具**: Maven
- **核心依赖**: spring-boot-starter-web, spring-boot-starter-webflux

## 项目结构

```
gateway/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── gateway/
│       │               ├── GatewayApplication.java
│       │               ├── config/
│       │               │   └── WebClientConfig.java
│       │               └── controller/
│       │                   └── ProxyController.java
│       └── resources/
│           ├── application.yml
│           └── application-dev.yml
```

## 功能特性

### 1. 完全透明转发
- 保持原始HTTP方法不变（GET、POST、PUT、DELETE、PATCH等）
- 完整转发所有原始请求头
- 原样转发所有请求体内容
- 完整转发所有响应头和响应体

### 2. 支持的内容类型
- application/json
- application/x-www-form-urlencoded
- multipart/form-data
- text/plain
- application/octet-stream
- 其他任意格式

### 3. 错误处理
- 后端服务不可用：返回502 Bad Gateway
- 请求超时：返回504 Gateway Timeout
- 连接失败：返回502 Bad Gateway
- 完整转发后端返回的错误响应

## 配置说明

### 配置变量来源

项目使用Spring Boot的配置属性机制，配置变量通过以下方式注入：

1. **配置文件定义**: 在`application.yml`中定义配置属性
2. **属性注入**: 在Java代码中使用`@Value`注解注入配置值

例如在`ProxyController.java`中：
```java
@Value("${gateway.backend.base-url}")
private String backendBaseUrl;

@Value("${gateway.backend.timeout:30000}")
private int timeout;
```

### application.yml
```yaml
server:
  port: 8080                    # Gateway监听端口

gateway:                        # 自定义配置命名空间
  backend:
    base-url: http://localhost  # 后端服务地址，对应 ${gateway.backend.base-url}
    timeout: 30000              # 请求超时时间(毫秒)，对应 ${gateway.backend.timeout}

logging:
  level:
    com.example.gateway: INFO   # 日志级别
```

### application-dev.yml
```yaml
gateway:
  backend:
    base-url: http://localhost  # 开发环境后端地址，会覆盖默认配置
```

### 配置优先级

Spring Boot配置加载优先级（从高到低）：
1. 命令行参数：`--gateway.backend.base-url=http://*************`
2. 环境变量：`GATEWAY_BACKEND_BASE_URL=http://*************`
3. profile特定配置：`application-dev.yml`
4. 默认配置：`application.yml`

## 部署配置

### 网络架构
```
前端应用 → Gateway(8080) → 后端服务(80)
```

### 部署时配置修改

#### 1. 前端项目配置修改
前端项目需要将所有API请求的基础URL修改为Gateway地址：

**修改前（直接访问后端）:**
```javascript
// 前端配置文件中
const API_BASE_URL = 'http://localhost/d/';
```

**修改后（通过Gateway访问）:**
```javascript
// 前端配置文件中
const API_BASE_URL = 'http://gateway-server:8080/d/';
```

#### 2. Gateway配置修改
修改`application.yml`或通过环境变量配置后端服务地址：

**方式一：修改配置文件**
```yaml
gateway:
  backend:
    base-url: http://backend-server  # 修改为实际后端服务地址
```

**方式二：环境变量**
```bash
export GATEWAY_BACKEND_BASE_URL=http://backend-server
```

**方式三：命令行参数**
```bash
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://backend-server
```

#### 3. 后端项目配置
后端服务**无需修改任何配置**，保持原有配置不变。

### 部署示例

#### 开发环境
```bash
# 1. 启动后端服务（端口80）
# 后端服务正常启动，无需修改

# 2. 启动Gateway（端口8080）
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://localhost

# 3. 前端配置API地址为Gateway
# API_BASE_URL = 'http://localhost:8080/d/'
```

#### 生产环境
```bash
# 1. 后端服务部署在 *************:80
# 2. Gateway部署在 *************:8080
java -jar gateway-1.0.0.jar --gateway.backend.base-url=http://*************

# 3. 前端配置API地址
# API_BASE_URL = 'http://*************:8080/d/'
```

## 使用方法

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 打包项目
```bash
mvn package -DskipTests
```

### 3. 运行应用
```bash
java -jar target/gateway-1.0.0.jar
```

### 4. 使用开发环境配置
```bash
java -jar target/gateway-1.0.0.jar --spring.profiles.active=dev
```

## 配置对照表

| 组件 | 配置项 | 说明 | 是否需要修改 |
|------|--------|------|-------------|
| **前端** | API_BASE_URL | API请求基础地址 | ✅ **需要修改**<br/>从 `http://backend/d/` 改为 `http://gateway:8080/d/` |
| **Gateway** | gateway.backend.base-url | 后端服务地址 | ✅ **需要修改**<br/>设置为实际后端服务地址 |
| **Gateway** | server.port | Gateway监听端口 | ❌ 通常不需要修改（默认8080） |
| **后端** | 所有配置 | 后端服务配置 | ❌ **无需修改**<br/>保持原有配置 |

## 常见部署场景

### 场景1：本地开发
- 后端：`http://localhost:80`
- Gateway：`http://localhost:8080`
- 前端API配置：`http://localhost:8080/d/`

### 场景2：Docker部署
```yaml
# docker-compose.yml
version: '3'
services:
  backend:
    # 后端服务配置
    ports:
      - "80:80"

  gateway:
    # Gateway配置
    ports:
      - "8080:8080"
    environment:
      - GATEWAY_BACKEND_BASE_URL=http://backend

  frontend:
    # 前端配置，API_BASE_URL=http://gateway:8080/d/
```

### 场景3：分布式部署
- 后端服务器：`*************:80`
- Gateway服务器：`*************:8080`
- 前端服务器：`*************:80`
- 前端API配置：`http://*************:8080/d/`

## API转发

网关监听端口8080，接收所有路径的请求：
- 接收路径：`http://localhost:8080/**`
- 转发目标：`http://localhost/**`

例如：
- 前端请求：`http://localhost:8080/d/login`
- 转发到：`http://localhost/d/login`

## 支持的API路径

根据前端和后端API列表，Gateway透明转发所有以下路径：

### 认证相关
- `/d/login`
- `/d/logout`
- `/d/auth`
- `/d/auth_status`

### 业务功能
- `/d/event`
- `/d/event_feature`
- `/d/evidence`
- `/d/asset`
- `/d/statinfo`
- `/d/feature`
- `/d/topn`
- `/d/config`
- `/d/mo`
- `/d/sctl`

### 工具接口
- `/d/geoinfo`
- `/d/portinfo`
- `/d/ipinfo`
- `/d/threatinfo`
- `/d/threatinfopro`
- `/d/locinfo`
- `/d/bwlist`
- `/d/internalip`

## 验收标准

1. **透明性**: 前端通过Gateway访问后端与直接访问后端结果完全一致
2. **完整性**: 所有请求头、响应头、请求体、响应体完全保持原样
3. **兼容性**: 支持前端和后端的所有现有API
4. **性能**: Gateway引入的额外延迟<50ms
5. **稳定性**: 长时间运行无内存泄漏，正确处理网络异常

## 注意事项

- 该网关为纯代理层，不包含任何业务逻辑
- 不修改任何请求和响应内容
- 确保后端服务在配置的地址上可用
- 根据实际环境调整配置文件中的后端服务地址
