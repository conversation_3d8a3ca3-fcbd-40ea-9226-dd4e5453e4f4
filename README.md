# Spring Boot Gateway 透明代理模块

## 项目概述

这是一个基于Spring Boot 2.7.7的透明代理网关模块，用于在前端和后端之间进行完全透明的API请求转发。该网关作为纯代理层，不修改任何请求和响应内容，不添加任何额外功能。

## 技术栈

- **Spring Boot**: 2.7.7
- **Java**: 8
- **构建工具**: Maven
- **核心依赖**: spring-boot-starter-web, spring-boot-starter-webflux

## 项目结构

```
gateway/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── gateway/
│       │               ├── GatewayApplication.java
│       │               ├── config/
│       │               │   └── WebClientConfig.java
│       │               └── controller/
│       │                   └── ProxyController.java
│       └── resources/
│           ├── application.yml
│           └── application-dev.yml
```

## 功能特性

### 1. 完全透明转发
- 保持原始HTTP方法不变（GET、POST、PUT、DELETE、PATCH等）
- 完整转发所有原始请求头
- 原样转发所有请求体内容
- 完整转发所有响应头和响应体

### 2. 支持的内容类型
- application/json
- application/x-www-form-urlencoded
- multipart/form-data
- text/plain
- application/octet-stream
- 其他任意格式

### 3. 错误处理
- 后端服务不可用：返回502 Bad Gateway
- 请求超时：返回504 Gateway Timeout
- 连接失败：返回502 Bad Gateway
- 完整转发后端返回的错误响应

## 配置说明

### application.yml
```yaml
server:
  port: 8080

gateway:
  backend:
    base-url: http://localhost  # 后端服务地址
    timeout: 30000  # 请求超时时间(毫秒)

logging:
  level:
    com.example.gateway: INFO
```

### application-dev.yml
```yaml
gateway:
  backend:
    base-url: http://localhost  # 开发环境后端地址
```

## 使用方法

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 打包项目
```bash
mvn package -DskipTests
```

### 3. 运行应用
```bash
java -jar target/gateway-1.0.0.jar
```

### 4. 使用开发环境配置
```bash
java -jar target/gateway-1.0.0.jar --spring.profiles.active=dev
```

## API转发

网关监听端口8080，接收所有路径的请求：
- 接收路径：`http://localhost:8080/**`
- 转发目标：`http://localhost/**`

例如：
- 前端请求：`http://localhost:8080/d/login`
- 转发到：`http://localhost/d/login`

## 支持的API路径

根据前端和后端API列表，Gateway透明转发所有以下路径：

### 认证相关
- `/d/login`
- `/d/logout`
- `/d/auth`
- `/d/auth_status`

### 业务功能
- `/d/event`
- `/d/event_feature`
- `/d/evidence`
- `/d/asset`
- `/d/statinfo`
- `/d/feature`
- `/d/topn`
- `/d/config`
- `/d/mo`
- `/d/sctl`

### 工具接口
- `/d/geoinfo`
- `/d/portinfo`
- `/d/ipinfo`
- `/d/threatinfo`
- `/d/threatinfopro`
- `/d/locinfo`
- `/d/bwlist`
- `/d/internalip`

## 验收标准

1. **透明性**: 前端通过Gateway访问后端与直接访问后端结果完全一致
2. **完整性**: 所有请求头、响应头、请求体、响应体完全保持原样
3. **兼容性**: 支持前端和后端的所有现有API
4. **性能**: Gateway引入的额外延迟<50ms
5. **稳定性**: 长时间运行无内存泄漏，正确处理网络异常

## 注意事项

- 该网关为纯代理层，不包含任何业务逻辑
- 不修改任何请求和响应内容
- 确保后端服务在配置的地址上可用
- 根据实际环境调整配置文件中的后端服务地址
