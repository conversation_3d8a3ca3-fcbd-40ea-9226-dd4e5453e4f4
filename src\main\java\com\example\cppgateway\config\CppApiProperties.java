package com.example.cppgateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * C++ API配置属性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "cpp.api")
public class CppApiProperties {

    /**
     * C++ API基础URL
     */
    private String baseUrl = "http://localhost:8081";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 路由配置
     */
    private Map<String, RouteConfig> routes;

    @Data
    public static class ConnectionPool {
        /**
         * 最大连接数
         */
        private int maxTotal = 100;

        /**
         * 每个路由的最大连接数
         */
        private int maxPerRoute = 20;

        /**
         * 连接不活跃后验证时间（毫秒）
         */
        private int validateAfterInactivity = 2000;
    }

    @Data
    public static class Retry {
        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 重试延迟时间（毫秒）
         */
        private long delay = 1000;
    }

    @Data
    public static class RouteConfig {
        /**
         * API路径
         */
        private String path;

        /**
         * 超时时间（毫秒）
         */
        private int timeout;
    }
}
