package com.example.gateway.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.util.Enumeration;

/**
 * 透明代理控制器
 * 完全透明地转发所有HTTP请求到后端服务
 */
@RestController
public class ProxyController {

    @Value("${gateway.backend.base-url}")
    private String backendBaseUrl;

    @Value("${gateway.backend.timeout:30000}")
    private int timeout;

    private final WebClient webClient;

    @Autowired
    public ProxyController(WebClient webClient) {
        this.webClient = webClient;
    }

    /**
     * 处理所有路径的请求，完全透明转发
     */
    @RequestMapping("/**")
    public void proxy(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            // 1. 获取原始请求信息
            String method = request.getMethod();
            String path = request.getRequestURI();
            String queryString = request.getQueryString();
            
            // 构建完整的后端URL
            String backendUrl = backendBaseUrl + path;
            if (queryString != null && !queryString.isEmpty()) {
                backendUrl += "?" + queryString;
            }

            // 2. 复制所有请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                Enumeration<String> headerValues = request.getHeaders(headerName);
                while (headerValues.hasMoreElements()) {
                    headers.add(headerName, headerValues.nextElement());
                }
            }

            // 3. 读取请求体
            byte[] requestBody = StreamUtils.copyToByteArray(request.getInputStream());

            // 4. 发送请求到后端
            WebClient.ResponseSpec responseSpec = webClient
                    .method(org.springframework.http.HttpMethod.valueOf(method))
                    .uri(backendUrl)
                    .headers(httpHeaders -> httpHeaders.addAll(headers))
                    .bodyValue(requestBody.length > 0 ? requestBody : "")
                    .retrieve();

            // 5. 获取后端响应
            ResponseEntity<byte[]> backendResponse = responseSpec
                    .toEntity(byte[].class)
                    .timeout(Duration.ofMillis(timeout))
                    .block();

            // 6. 将后端响应原样转发给前端
            if (backendResponse != null) {
                // 设置响应状态码
                response.setStatus(backendResponse.getStatusCodeValue());
                
                // 复制所有响应头
                HttpHeaders responseHeaders = backendResponse.getHeaders();
                for (String headerName : responseHeaders.keySet()) {
                    for (String headerValue : responseHeaders.get(headerName)) {
                        response.addHeader(headerName, headerValue);
                    }
                }
                
                // 写入响应体
                byte[] responseBody = backendResponse.getBody();
                if (responseBody != null && responseBody.length > 0) {
                    response.getOutputStream().write(responseBody);
                }
            }

        } catch (WebClientRequestException e) {
            // 连接失败或超时
            handleNetworkError(response, e);
        } catch (WebClientResponseException e) {
            // 后端返回的错误响应，原样转发
            handleBackendError(response, e);
        } catch (Exception e) {
            // 其他异常（包括超时）
            handleGenericError(response, e);
        }
    }

    /**
     * 处理网络连接错误
     */
    private void handleNetworkError(HttpServletResponse response, WebClientRequestException e) throws IOException {
        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        response.setContentType("text/plain; charset=UTF-8");
        response.getWriter().write("502 Bad Gateway");
    }

    /**
     * 处理后端服务返回的错误，完全透明转发
     */
    private void handleBackendError(HttpServletResponse response, WebClientResponseException e) throws IOException {
        // 设置状态码
        response.setStatus(e.getRawStatusCode());

        // 复制所有响应头
        e.getHeaders().forEach((name, values) -> {
            for (String value : values) {
                response.addHeader(name, value);
            }
        });

        // 写入响应体
        byte[] errorBody = e.getResponseBodyAsByteArray();
        if (errorBody != null && errorBody.length > 0) {
            response.getOutputStream().write(errorBody);
        }
    }

    /**
     * 处理超时错误
     */
    private void handleTimeoutError(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.GATEWAY_TIMEOUT.value());
        response.setContentType("text/plain; charset=UTF-8");
        response.getWriter().write("504 Gateway Timeout");
    }

    /**
     * 处理通用错误
     */
    private void handleGenericError(HttpServletResponse response, Exception e) throws IOException {
        response.setStatus(HttpStatus.BAD_GATEWAY.value());
        response.setContentType("text/plain; charset=UTF-8");
        response.getWriter().write("502 Bad Gateway");
    }
}
