server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: cpp-api-gateway
  
  # Jackson配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# C++ API配置
cpp:
  api:
    # C++ RESTful API的基础URL
    base-url: http://localhost:8081
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 30000
    # 连接池配置
    connection-pool:
      max-total: 100
      max-per-route: 20
      validate-after-inactivity: 2000
    # 重试配置
    retry:
      max-attempts: 3
      delay: 1000
    # 路由配置（后续可以根据实际API路径进行配置）
    routes:
      # 示例路由配置
      user:
        path: /users
        timeout: 10000
      data:
        path: /data
        timeout: 15000

# 日志配置
logging:
  level:
    com.example.cppgateway: DEBUG
    org.springframework.web.client: DEBUG
    org.apache.http: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
