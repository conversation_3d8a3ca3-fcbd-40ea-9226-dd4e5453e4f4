{"groups": [{"name": "cpp.api", "type": "com.example.cppgateway.config.CppApiProperties", "sourceType": "com.example.cppgateway.config.CppApiProperties"}, {"name": "cpp.api.connection-pool", "type": "com.example.cppgateway.config.CppApiProperties$ConnectionPool", "sourceType": "com.example.cppgateway.config.CppApiProperties"}, {"name": "cpp.api.retry", "type": "com.example.cppgateway.config.CppApiProperties$Retry", "sourceType": "com.example.cppgateway.config.CppApiProperties"}], "properties": [{"name": "cpp.api.base-url", "type": "java.lang.String", "description": "C++ API基础URL", "sourceType": "com.example.cppgateway.config.CppApiProperties", "defaultValue": "http://localhost:8081"}, {"name": "cpp.api.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.example.cppgateway.config.CppApiProperties", "defaultValue": 5000}, {"name": "cpp.api.connection-pool.max-per-route", "type": "java.lang.Integer", "description": "每个路由的最大连接数", "sourceType": "com.example.cppgateway.config.CppApiProperties$ConnectionPool", "defaultValue": 20}, {"name": "cpp.api.connection-pool.max-total", "type": "java.lang.Integer", "description": "最大连接数", "sourceType": "com.example.cppgateway.config.CppApiProperties$ConnectionPool", "defaultValue": 100}, {"name": "cpp.api.connection-pool.validate-after-inactivity", "type": "java.lang.Integer", "description": "连接不活跃后验证时间（毫秒）", "sourceType": "com.example.cppgateway.config.CppApiProperties$ConnectionPool", "defaultValue": 2000}, {"name": "cpp.api.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "com.example.cppgateway.config.CppApiProperties", "defaultValue": 30000}, {"name": "cpp.api.retry.delay", "type": "java.lang.Long", "description": "重试延迟时间（毫秒）", "sourceType": "com.example.cppgateway.config.CppApiProperties$Retry", "defaultValue": 1000}, {"name": "cpp.api.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "com.example.cppgateway.config.CppApiProperties$Retry", "defaultValue": 3}, {"name": "cpp.api.routes", "type": "java.util.Map<java.lang.String,com.example.cppgateway.config.CppApiProperties$RouteConfig>", "description": "路由配置", "sourceType": "com.example.cppgateway.config.CppApiProperties"}], "hints": []}